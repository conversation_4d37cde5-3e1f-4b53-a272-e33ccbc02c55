model,fit_time_sec,cv_time_sec,inference_time_sec,inference_time_per_sample_ms,param_count
Decision Tree,0.01482609985396266,,0.0021184999495744705,0.021184999495744705,
<PERSON><PERSON><PERSON>,6.67736169998534,,0.003663100069388747,0.03663100069388747,
<PERSON>t<PERSON><PERSON><PERSON>,0.7087743999436498,,0.0077580001670867205,0.0775800016708672,
K-Nearest Neighbors,0.0065213998313993216,,0.09418330015614629,0.9418330015614629,
Logistic Regression,0.10606790008023381,,0.0016639998648315668,0.016639998648315668,864
<PERSON><PERSON>,0.008964800043031573,,0.0015694000758230686,0.015694000758230686,
Neural Network,0.7145243000704795,,0.0015251999720931053,0.015251999720931053,
Random <PERSON>,0.1767802000977099,,0.02989399991929531,0.2989399991929531,
SVM (Linear),0.2126407001633197,,0.002395300194621086,0.02395300194621086,4752
SVM (RBF),0.2702297999057919,,0.008604899980127811,0.08604899980127811,1337
XGBoost,0.5332702999003232,,0.0333445998840034,0.333445998840034,
