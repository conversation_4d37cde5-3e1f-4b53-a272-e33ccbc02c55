{"metadata": {"model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "run_id": "20251005_175806", "timestamp": "2025-10-05T19:37:56.806224+00:00", "n_samples": 100, "n_features": 71, "shap_values_shape": [100, 71, 12], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_char_count", "mean_abs_shap": 0.03291239862843628, "std_abs_shap": 0.0698476770717686, "max_abs_shap": 0.7728290102860826, "feature_index": 13}, {"rank": 2, "feature_name": "feat_word_count", "mean_abs_shap": 0.03205547709854552, "std_abs_shap": 0.07387608179082342, "max_abs_shap": 0.7250576428734361, "feature_index": 70}, {"rank": 3, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.025844388729317064, "std_abs_shap": 0.06941435148143521, "max_abs_shap": 0.658640218197712, "feature_index": 49}, {"rank": 4, "feature_name": "feat_return_statements", "mean_abs_shap": 0.02022078910747936, "std_abs_shap": 0.06258097137140343, "max_abs_shap": 0.745702148436741, "feature_index": 58}, {"rank": 5, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.011490951774414013, "std_abs_shap": 0.030270164124916975, "max_abs_shap": 0.2957882770529494, "feature_index": 44}, {"rank": 6, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.01083324382785153, "std_abs_shap": 0.024891040859328428, "max_abs_shap": 0.21101070165109992, "feature_index": 55}, {"rank": 7, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.008832823275659953, "std_abs_shap": 0.02465115148030523, "max_abs_shap": 0.4284049490932476, "feature_index": 0}, {"rank": 8, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.008021558395268993, "std_abs_shap": 0.02034485053516727, "max_abs_shap": 0.26997530966626554, "feature_index": 64}, {"rank": 9, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.007762113419827233, "std_abs_shap": 0.01848052000338169, "max_abs_shap": 0.17161003266394906, "feature_index": 5}, {"rank": 10, "feature_name": "feat_function_calls", "mean_abs_shap": 0.007576892655837333, "std_abs_shap": 0.019565620542228178, "max_abs_shap": 0.2766361711793996, "feature_index": 25}, {"rank": 11, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.006557054281552267, "std_abs_shap": 0.02060690605836363, "max_abs_shap": 0.27630332490799303, "feature_index": 48}, {"rank": 12, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.006435918461477998, "std_abs_shap": 0.019325805529240375, "max_abs_shap": 0.18897383875424087, "feature_index": 3}, {"rank": 13, "feature_name": "feat_if_statements", "mean_abs_shap": 0.005490505589691943, "std_abs_shap": 0.021572594748339644, "max_abs_shap": 0.2919919089894544, "feature_index": 31}, {"rank": 14, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.005373796386327462, "std_abs_shap": 0.014485430368917114, "max_abs_shap": 0.17444574481928765, "feature_index": 63}, {"rank": 15, "feature_name": "feat_avg_function_complexity", "mean_abs_shap": 0.004051226382551839, "std_abs_shap": 0.013368432806041169, "max_abs_shap": 0.11891533786552078, "feature_index": 6}, {"rank": 16, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.0035142562158312803, "std_abs_shap": 0.014437949404942368, "max_abs_shap": 0.14880683314903387, "feature_index": 12}, {"rank": 17, "feature_name": "feat_assignments", "mean_abs_shap": 0.0033687572138212953, "std_abs_shap": 0.012130923528592767, "max_abs_shap": 0.13957481401136776, "feature_index": 2}, {"rank": 18, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.003285506195299269, "std_abs_shap": 0.012349933142590221, "max_abs_shap": 0.11440755401583473, "feature_index": 16}, {"rank": 19, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.0023693935697986297, "std_abs_shap": 0.009117400665666554, "max_abs_shap": 0.16701630164398207, "feature_index": 34}, {"rank": 20, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.002257392702147867, "std_abs_shap": 0.013690366541742984, "max_abs_shap": 0.20400699651065823, "feature_index": 51}], "total_importance": 0.22439185156424846, "top_10_importance_ratio": 0.7377747264821529}, "feature_statistics": {"mean_importance": 0.003160448613580964, "median_importance": 0.00011453999083184625, "std_importance": 0.0067268642561852205, "max_importance": 0.03291239862843628, "min_importance": 0.0, "importance_concentration": 0.546026981301105}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.011490951774414013, "rank": 44}, {"name": "feat_nodes_with_children", "importance": 0.01083324382785153, "rank": 55}, {"name": "feat_tree_depth", "importance": 0.008021558395268993, "rank": 64}, {"name": "feat_total_nodes", "importance": 0.005373796386327462, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.0010247882607708318, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.005490505589691943, "rank": 31}, {"name": "feat_for_loops", "importance": 0.0014748481569344086, "rank": 24}, {"name": "feat_string_formatting", "importance": 0.0008173371670823322, "rank": 61}, {"name": "feat_nested_loops", "importance": 0.0004582762669491841, "rank": 54}, {"name": "feat_if_else_chains", "importance": 0.0003796554692153011, "rank": 30}, {"name": "feat_nested_ifs", "importance": 0.0002984021439679831, "rank": 53}, {"name": "feat_keyword_for_count", "importance": 4.13137949036962e-05, "rank": 36}, {"name": "feat_try_except_blocks", "importance": 3.562154514458909e-05, "rank": 65}, {"name": "feat_try_statements", "importance": 1.8420875429078526e-05, "rank": 67}, {"name": "feat_while_loops", "importance": 1.3222920064872703e-05, "rank": 68}, {"name": "feat_keyword_if_count", "importance": 1.3142336207514737e-05, "rank": 37}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}], "complexity_features": [{"name": "feat_avg_branching_factor", "importance": 0.007762113419827233, "rank": 5}, {"name": "feat_max_branching_factor", "importance": 0.006557054281552267, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.004051226382551839, "rank": 6}, {"name": "feat_cyclomatic_complexity", "importance": 0.0012320680407314447, "rank": 19}, {"name": "feat_max_function_complexity", "importance": 0.0009631811255116379, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.025844388729317064, "rank": 49}, {"name": "feat_function_calls", "importance": 0.007576892655837333, "rank": 25}, {"name": "feat_attribute_access", "importance": 0.006435918461477998, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.0035142562158312803, "rank": 12}, {"name": "feat_assignments", "importance": 0.0033687572138212953, "rank": 2}, {"name": "feat_aug_assignments", "importance": 1.2351570908154079e-05, "rank": 4}], "other_features": [{"name": "feat_char_count", "importance": 0.03291239862843628, "rank": 13}, {"name": "feat_word_count", "importance": 0.03205547709854552, "rank": 70}, {"name": "feat_return_statements", "importance": 0.02022078910747936, "rank": 58}, {"name": "feat_arithmetic_ops", "importance": 0.008832823275659953, "rank": 0}, {"name": "feat_comparison_ops", "importance": 0.003285506195299269, "rank": 16}, {"name": "feat_keyword_def_count", "importance": 0.0023693935697986297, "rank": 34}, {"name": "feat_max_function_params", "importance": 0.002257392702147867, "rank": 51}, {"name": "feat_colon_count", "importance": 0.0019436296115010047, "rank": 15}, {"name": "feat_subscript_access", "importance": 0.0018120580707920108, "rank": 62}, {"name": "feat_import_statements", "importance": 0.001510549218576572, "rank": 32}, {"name": "feat_bracket_count", "importance": 0.0014253462804750634, "rank": 10}, {"name": "feat_equals_count", "importance": 0.0011029175879283153, "rank": 23}, {"name": "feat_line_count", "importance": 0.0005476341501066922, "rank": 45}, {"name": "feat_paren_count", "importance": 0.0004380012017998495, "rank": 56}, {"name": "feat_keyword_class_count", "importance": 0.00011453999083184625, "rank": 33}, {"name": "feat_keyword_import_count", "importance": 0.00010910853033529425, "rank": 38}, {"name": "feat_list_comprehensions", "importance": 5.4046302669057424e-05, "rank": 46}, {"name": "feat_class_defs", "importance": 5.177545480979526e-05, "rank": 14}, {"name": "feat_keyword_return_count", "importance": 4.859759408602771e-05, "rank": 39}, {"name": "feat_dict_comps", "importance": 4.300492445623187e-05, "rank": 22}, {"name": "feat_function_defs", "importance": 2.768791355538312e-05, "rank": 26}, {"name": "feat_generator_expressions", "importance": 2.2767060666912423e-05, "rank": 27}, {"name": "feat_dict_comprehensions", "importance": 2.129956360597633e-05, "rank": 21}, {"name": "feat_list_comps", "importance": 2.0583147960645522e-05, "rank": 47}, {"name": "feat_boolean_ops", "importance": 1.865250488610375e-05, "rank": 8}, {"name": "feat_has_syntax_error", "importance": 1.634214255562384e-05, "rank": 29}, {"name": "feat_generator_exps", "importance": 1.102709845861293e-05, "rank": 28}, {"name": "feat_lambda_functions", "importance": 6.953589019459025e-06, "rank": 42}, {"name": "feat_lambda_usage", "importance": 5.824117961643796e-06, "rank": 43}, {"name": "feat_decorator_usage", "importance": 2.431922252216185e-06, "rank": 20}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_brace_count", "importance": 0.0, "rank": 9}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.03674433864463283, "control_flow_features": 0.009040746265590904, "complexity_features": 0.02056564325017442, "syntactic_features": 0.046752564847193125, "other_features": 0.11128855855665722}}}, "visualisations_generated": ["shap_summary_histgradient_boosting_20251005_175806.png", "shap_bar_histgradient_boosting_20251005_175806.png", "shap_waterfall_histgradient_boosting_sample1_20251005_175806.png", "shap_waterfall_histgradient_boosting_sample2_20251005_175806.png", "shap_waterfall_histgradient_boosting_sample3_20251005_175806.png", "shap_dependence_histgradient_boosting_feat1_20251005_175806.png", "shap_dependence_histgradient_boosting_feat2_20251005_175806.png"]}