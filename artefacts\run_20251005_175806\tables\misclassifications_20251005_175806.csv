i,actual,pred,snippet
1,Recursion<PERSON><PERSON><PERSON><PERSON>otential,No<PERSON><PERSON>r,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           6
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
3,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
4,Type<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
6,LogicE<PERSON>rNegation,NoError,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
7,Synta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,SyntaxErrorMissingColon,"feat_arithmetic_ops         0
feat_assert_statements      0
feat_assignments            0
feat_attribute_access       0
feat_aug_assignments        0
                           ..
feat_try_finally_blo"
11,Varia<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
12,Syntax<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>olo<PERSON>,SyntaxE<PERSON>r<PERSON>ismatched<PERSON>aren,"feat_arithmetic_ops         0
feat_assert_statements      0
feat_assignments            0
feat_attribute_access       0
feat_aug_assignments        0
                           ..
feat_try_finally_blo"
13,LogicErrorComparison,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           3
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
14,VariableError,NoError,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
15,TypeErrorArity,RecursionErrorPotential,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
16,VariableError,NoError,"feat_arithmetic_ops        2
feat_assert_statements     0
feat_assignments           5
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
18,LogicErrorComparison,NoError,"feat_arithmetic_ops        2
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
19,TypeErrorArity,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      0
feat_aug_assignments       1
                          ..
feat_try_finally_blocks   "
21,LogicErrorComparison,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      0
feat_aug_assignments       1
                          ..
feat_try_finally_blocks   "
22,TypeErrorArity,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           3
feat_attribute_access      3
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
24,VariableError,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
26,VariableError,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      3
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
28,DataAccessError,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
29,VariableError,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
30,LogicErrorNegation,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
31,TypeErrorArity,NoError,"feat_arithmetic_ops        4
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
33,SyntaxErrorMissingColon,SyntaxErrorMismatchedParen,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
34,VariableError,NoError,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      0
feat_aug_assignments       1
                          ..
feat_try_finally_blocks   "
38,LogicErrorNegation,VariableError,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
39,TypeErrorArity,VariableError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      3
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
40,RecursionErrorPotential,TypeErrorArity,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
41,TypeErrorArity,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
42,VariableError,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
43,TypeErrorBadKwarg,TypeErrorArity,"feat_arithmetic_ops        4
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
44,SyntaxErrorMissingColon,SyntaxErrorMismatchedParen,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
45,VariableError,NoError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      4
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
48,TypeErrorBadAdd,NoError,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           3
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
50,NoError,VariableError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
58,NoError,TypeErrorArity,"feat_arithmetic_ops        5
feat_assert_statements     0
feat_assignments           3
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
59,NoError,VariableError,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
65,NoError,RecursionErrorPotential,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
72,NoError,VariableError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           3
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
74,NoError,TypeErrorArity,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
75,NoError,VariableError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           2
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
77,NoError,VariableError,"feat_arithmetic_ops        4
feat_assert_statements     0
feat_assignments           4
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
83,NoError,VariableError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      1
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
88,NoError,VariableError,"feat_arithmetic_ops        1
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
89,NoError,VariableError,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      3
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
90,NoError,TypeErrorArity,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
93,NoError,TypeErrorArity,"feat_arithmetic_ops        4
feat_assert_statements     0
feat_assignments           1
feat_attribute_access      2
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
96,NoError,TypeErrorArity,"feat_arithmetic_ops        0
feat_assert_statements     0
feat_assignments           0
feat_attribute_access      0
feat_aug_assignments       0
                          ..
feat_try_finally_blocks   "
