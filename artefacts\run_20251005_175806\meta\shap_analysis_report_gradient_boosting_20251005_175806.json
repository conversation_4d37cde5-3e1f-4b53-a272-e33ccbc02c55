{"metadata": {"model_name": "Grad<PERSON>", "run_id": "20251005_175806", "timestamp": "2025-10-05T19:37:14.642820+00:00", "n_samples": 100, "n_features": 71, "shap_values_shape": [100, 71, 12], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_char_count", "mean_abs_shap": 0.030041644593385684, "std_abs_shap": 0.07145603099521507, "max_abs_shap": 0.7340508475534925, "feature_index": 13}, {"rank": 2, "feature_name": "feat_word_count", "mean_abs_shap": 0.026638806476375152, "std_abs_shap": 0.06738158280231248, "max_abs_shap": 0.7397489813731251, "feature_index": 70}, {"rank": 3, "feature_name": "feat_return_statements", "mean_abs_shap": 0.02268339804196627, "std_abs_shap": 0.0710876202570009, "max_abs_shap": 0.7789534292505813, "feature_index": 58}, {"rank": 4, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.014264967917542129, "std_abs_shap": 0.047451595719665805, "max_abs_shap": 0.5830793439995205, "feature_index": 49}, {"rank": 5, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.010856070990460378, "std_abs_shap": 0.031233172579544045, "max_abs_shap": 0.24392843784666293, "feature_index": 44}, {"rank": 6, "feature_name": "feat_colon_count", "mean_abs_shap": 0.010542727607532307, "std_abs_shap": 0.029358740433807137, "max_abs_shap": 0.33999541264674454, "feature_index": 15}, {"rank": 7, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.008247432874909146, "std_abs_shap": 0.025193626637370254, "max_abs_shap": 0.40512885417093697, "feature_index": 64}, {"rank": 8, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.0069790544016124076, "std_abs_shap": 0.02189373586686276, "max_abs_shap": 0.2088623805183588, "feature_index": 3}, {"rank": 9, "feature_name": "feat_if_statements", "mean_abs_shap": 0.006493044629708192, "std_abs_shap": 0.03258713381375075, "max_abs_shap": 0.4479748046388755, "feature_index": 31}, {"rank": 10, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.0062059518220626405, "std_abs_shap": 0.01855003133746317, "max_abs_shap": 0.13723328835312842, "feature_index": 55}, {"rank": 11, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.006181196496376096, "std_abs_shap": 0.017649235285585076, "max_abs_shap": 0.18389959908224449, "feature_index": 63}, {"rank": 12, "feature_name": "feat_keyword_for_count", "mean_abs_shap": 0.005072946818926119, "std_abs_shap": 0.0187199015940782, "max_abs_shap": 0.22200812883881887, "feature_index": 36}, {"rank": 13, "feature_name": "feat_function_calls", "mean_abs_shap": 0.00492883664171578, "std_abs_shap": 0.01292655188658629, "max_abs_shap": 0.19207008416659038, "feature_index": 25}, {"rank": 14, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.004283369272573718, "std_abs_shap": 0.014860426261330556, "max_abs_shap": 0.14205407759465333, "feature_index": 48}, {"rank": 15, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.003998033037947094, "std_abs_shap": 0.014976443849595823, "max_abs_shap": 0.314420891287075, "feature_index": 0}, {"rank": 16, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.0031583552221303357, "std_abs_shap": 0.01577448079327078, "max_abs_shap": 0.23448731615002105, "feature_index": 51}, {"rank": 17, "feature_name": "feat_bracket_count", "mean_abs_shap": 0.0030464873847601123, "std_abs_shap": 0.014087218726177419, "max_abs_shap": 0.2680578991834782, "feature_index": 10}, {"rank": 18, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.0025787674713911007, "std_abs_shap": 0.00952202262726127, "max_abs_shap": 0.1399185950320826, "feature_index": 34}, {"rank": 19, "feature_name": "feat_assignments", "mean_abs_shap": 0.0025773685174702867, "std_abs_shap": 0.008388409968383727, "max_abs_shap": 0.08054905981531423, "feature_index": 2}, {"rank": 20, "feature_name": "feat_import_statements", "mean_abs_shap": 0.0023509838138412445, "std_abs_shap": 0.009909286301794072, "max_abs_shap": 0.12094689489021118, "feature_index": 32}], "total_importance": 0.19662080225288694, "top_10_importance_ratio": 0.7270497206683803}, "feature_statistics": {"mean_importance": 0.0027693070739843234, "median_importance": 0.00021703539027173317, "std_importance": 0.005804664712362142, "max_importance": 0.030041644593385684, "min_importance": 0.0, "importance_concentration": 0.5314030195306839}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.010856070990460378, "rank": 44}, {"name": "feat_tree_depth", "importance": 0.008247432874909146, "rank": 64}, {"name": "feat_nodes_with_children", "importance": 0.0062059518220626405, "rank": 55}, {"name": "feat_total_nodes", "importance": 0.006181196496376096, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 7.223366248065976e-05, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.006493044629708192, "rank": 31}, {"name": "feat_keyword_for_count", "importance": 0.005072946818926119, "rank": 36}, {"name": "feat_if_else_chains", "importance": 0.0011497815404877803, "rank": 30}, {"name": "feat_string_formatting", "importance": 0.00045422741449762603, "rank": 61}, {"name": "feat_while_loops", "importance": 8.79062026266909e-05, "rank": 68}, {"name": "feat_for_loops", "importance": 8.242634958129441e-05, "rank": 24}, {"name": "feat_nested_loops", "importance": 3.990779733731642e-05, "rank": 54}, {"name": "feat_try_except_blocks", "importance": 2.3531393877027882e-05, "rank": 65}, {"name": "feat_try_statements", "importance": 1.7887971426158504e-05, "rank": 67}, {"name": "feat_keyword_if_count", "importance": 1.4714365972267294e-05, "rank": 37}, {"name": "feat_nested_ifs", "importance": 6.199366825608762e-06, "rank": 53}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}], "complexity_features": [{"name": "feat_max_branching_factor", "importance": 0.004283369272573718, "rank": 48}, {"name": "feat_cyclomatic_complexity", "importance": 0.0004887840423277436, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.0004028883574358139, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00013499235029800806, "rank": 50}, {"name": "feat_avg_branching_factor", "importance": 0.0001241069017711595, "rank": 5}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.014264967917542129, "rank": 49}, {"name": "feat_attribute_access", "importance": 0.0069790544016124076, "rank": 3}, {"name": "feat_function_calls", "importance": 0.00492883664171578, "rank": 25}, {"name": "feat_assignments", "importance": 0.0025773685174702867, "rank": 2}, {"name": "feat_builtin_calls", "importance": 0.002056132291646522, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.0008016911823241521, "rank": 4}], "other_features": [{"name": "feat_char_count", "importance": 0.030041644593385684, "rank": 13}, {"name": "feat_word_count", "importance": 0.026638806476375152, "rank": 70}, {"name": "feat_return_statements", "importance": 0.02268339804196627, "rank": 58}, {"name": "feat_colon_count", "importance": 0.010542727607532307, "rank": 15}, {"name": "feat_arithmetic_ops", "importance": 0.003998033037947094, "rank": 0}, {"name": "feat_max_function_params", "importance": 0.0031583552221303357, "rank": 51}, {"name": "feat_bracket_count", "importance": 0.0030464873847601123, "rank": 10}, {"name": "feat_keyword_def_count", "importance": 0.0025787674713911007, "rank": 34}, {"name": "feat_import_statements", "importance": 0.0023509838138412445, "rank": 32}, {"name": "feat_line_count", "importance": 0.002256420130358723, "rank": 45}, {"name": "feat_keyword_return_count", "importance": 0.001895261396723427, "rank": 39}, {"name": "feat_comparison_ops", "importance": 0.0015735776366441803, "rank": 16}, {"name": "feat_lambda_functions", "importance": 0.000690216498294285, "rank": 42}, {"name": "feat_paren_count", "importance": 0.0006745148113742584, "rank": 56}, {"name": "feat_subscript_access", "importance": 0.00040265785008331744, "rank": 62}, {"name": "feat_function_defs", "importance": 0.00039682968219276584, "rank": 26}, {"name": "feat_list_comps", "importance": 0.000294895511298642, "rank": 47}, {"name": "feat_list_comprehensions", "importance": 0.00027966258962313403, "rank": 46}, {"name": "feat_decorator_usage", "importance": 0.00021703539027173317, "rank": 20}, {"name": "feat_equals_count", "importance": 0.00020171643374937561, "rank": 23}, {"name": "feat_dict_comps", "importance": 0.00017111573862980538, "rank": 22}, {"name": "feat_boolean_ops", "importance": 0.00013618300066938708, "rank": 8}, {"name": "feat_lambda_usage", "importance": 8.629469929099682e-05, "rank": 43}, {"name": "feat_keyword_class_count", "importance": 6.867498767691602e-05, "rank": 33}, {"name": "feat_generator_expressions", "importance": 5.625558069987365e-05, "rank": 27}, {"name": "feat_class_defs", "importance": 4.298358815371486e-05, "rank": 14}, {"name": "feat_keyword_import_count", "importance": 3.794225368356681e-05, "rank": 38}, {"name": "feat_generator_exps", "importance": 2.8144839068277803e-05, "rank": 28}, {"name": "feat_has_syntax_error", "importance": 1.4296884200427924e-05, "rank": 29}, {"name": "feat_dict_comprehensions", "importance": 9.267526598105294e-06, "rank": 21}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_brace_count", "importance": 0.0, "rank": 9}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.03156288584628892, "control_flow_features": 0.013442573851266081, "complexity_features": 0.005434140924406443, "syntactic_features": 0.03160805095231128, "other_features": 0.11457315067861422}}}, "visualisations_generated": ["shap_summary_gradient_boosting_20251005_175806.png", "shap_bar_gradient_boosting_20251005_175806.png", "shap_waterfall_gradient_boosting_sample1_20251005_175806.png", "shap_waterfall_gradient_boosting_sample2_20251005_175806.png", "shap_waterfall_gradient_boosting_sample3_20251005_175806.png", "shap_dependence_gradient_boosting_feat1_20251005_175806.png", "shap_dependence_gradient_boosting_feat2_20251005_175806.png"]}