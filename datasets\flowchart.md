```mermaid
---
config:
  look: handDrawn
  theme: neutral
---
flowchart LR
    A[Input: Python Code Snippet] --> B{Parse Code with AST}
    B -->|Success| C[Shuffle Error Injector Registry]
    B -->|SyntaxError| D[Return: Input Invalid]
    
    C --> E[Try Each Injector Function]
    E --> F{Injector Success?}
    F -->|No| G{More Injectors?}
    G -->|Yes| E
    G -->|No| H[Fallback Textual Injection]
    
    F -->|Yes| I{Error Category?}
    I -->|Syntax| J[Apply Syntax Text Transform]
    I -->|Runtime| K[Convert AST to Source]
    I -->|Logic| K
    
    J --> L[Return: Syntax Error Code + Metadata]
    
    K --> M{Source Conversion Success?}
    M -->|No| H
    M -->|Yes| N{Category = Runtime?}
    
    N -->|No Logic| O[Skip Verification]
    N -->|Yes| P[Hybrid Runtime Verification]
    P <-->A1
    P <-->A2
    P <-->A3
    P <-->A4
    
    A1[Pylint Analysis]
    A2[MyPy Analysis]
    A3[Subprocess Execution]
    A4[Analyse Code Pipeline]

    P --> Q{Verification Success?}
    Q -->|Yes| R[Extract Runtime Error Info]
    Q -->|No| S[Mark Verification Failed]
    
    O --> T[Compile Final Metadata]
    R --> T
    S --> T
    
    T --> U[Return: Mutated Code + Metadata]
    
    H --> V[Remove Colon or Add Division by Zero]
    V --> W[Return: Fallback Code + Metadata]
    
    style A fill:#e1f5fe
    style U fill:#c8e6c9
    style W fill:#ffccbc
    style L fill:#f3e5f5
    style D fill:#ffcdd2
    style B fill:#e1f5fe


```