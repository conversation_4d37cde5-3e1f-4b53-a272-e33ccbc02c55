train_samples,train_score,test_score,scoring
20,1.0,0.22704365079365077,balanced_accuracy
40,1.0,0.1363690476190476,balanced_accuracy
60,0.9966666666666667,0.15303571428571427,balanced_accuracy
80,0.990909090909091,0.2776190476190476,balanced_accuracy
100,0.9916666666666667,0.31898809523809524,balanced_accuracy
120,0.986764705882353,0.20398809523809525,balanced_accuracy
140,0.9916666666666667,0.21982142857142858,balanced_accuracy
160,0.9878787878787879,0.3010714285714286,balanced_accuracy
180,0.9916666666666667,0.25351190476190477,balanced_accuracy
200,0.9874999999999999,0.2882142857142857,balanced_accuracy
221,0.9893939393939394,0.3761904761904762,balanced_accuracy
240,0.9871323529411765,0.3388690476190476,balanced_accuracy
260,0.9842236467236467,0.32107142857142856,balanced_accuracy
280,0.9857142857142858,0.33827380952380953,balanced_accuracy
300,0.9824603174603174,0.3429761904761905,balanced_accuracy
320,0.9820023148148148,0.34660714285714284,balanced_accuracy
340,0.9768790849673201,0.37767857142857136,balanced_accuracy
360,0.9781481481481483,0.34922619047619047,balanced_accuracy
380,0.9773750413770275,0.3535119047619047,balanced_accuracy
400,0.9715476190476191,0.3463095238095238,balanced_accuracy
