{"model": "GradientBoostingClassifier", "top_n": 20, "classes": ["ArithmeticError", "DataAccessError", "LogicErrorComparison", "LogicErrorNegation", "NoError", "RecursionErrorPotential", "SyntaxErrorMismatchedParen", "SyntaxErrorMissingColon", "TypeErrorArity", "TypeErrorBadAdd", "TypeErrorBadKwarg", "VariableError"], "global_top_tokens": [{"token": "f4", "mean_abs_shap": 0.013928634541901833}, {"token": "f6", "mean_abs_shap": 0.007375805623696439}, {"token": "f11", "mean_abs_shap": 0.006452016042981311}, {"token": "f8", "mean_abs_shap": 0.005991600114636826}, {"token": "f7", "mean_abs_shap": 0.0057649215127073905}, {"token": "f5", "mean_abs_shap": 0.003157501113863456}, {"token": "f0", "mean_abs_shap": 0.0015216076697565412}, {"token": "f3", "mean_abs_shap": 0.0003984888757742055}, {"token": "f2", "mean_abs_shap": 0.00017234250330657458}, {"token": "f1", "mean_abs_shap": 9.464067025254982e-05}, {"token": "f10", "mean_abs_shap": 1.3109391768276455e-06}, {"token": "f9", "mean_abs_shap": 8.776069979195188e-10}], "per_class_top_tokens": {"ArithmeticError": [{"token": "f0", "mean_abs_shap": 0.024140228118585867}, {"token": "f4", "mean_abs_shap": 0.01806791763526064}, {"token": "f8", "mean_abs_shap": 0.010046340492373984}, {"token": "f11", "mean_abs_shap": 0.008823105364224412}, {"token": "f6", "mean_abs_shap": 0.005248575640512735}, {"token": "f7", "mean_abs_shap": 0.001823574164759363}, {"token": "f5", "mean_abs_shap": 0.0017251105123940063}, {"token": "f1", "mean_abs_shap": 0.0005616947570502313}, {"token": "f3", "mean_abs_shap": 0.0004515540185961611}, {"token": "f2", "mean_abs_shap": 0.00015552624409736365}, {"token": "f10", "mean_abs_shap": 1.8656188880133128e-08}, {"token": "f9", "mean_abs_shap": 2.4008703877897743e-09}], "DataAccessError": [{"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "LogicErrorComparison": [{"token": "f4", "mean_abs_shap": 0.020573913098317985}, {"token": "f11", "mean_abs_shap": 0.01660610877786801}, {"token": "f8", "mean_abs_shap": 0.009026555142120035}, {"token": "f0", "mean_abs_shap": 0.004228511419544358}, {"token": "f6", "mean_abs_shap": 0.0018640092690809808}, {"token": "f7", "mean_abs_shap": 0.0017747253169737944}, {"token": "f5", "mean_abs_shap": 0.0011602719584669003}, {"token": "f3", "mean_abs_shap": 0.0004905348033244064}, {"token": "f2", "mean_abs_shap": 0.0003783956870935894}, {"token": "f1", "mean_abs_shap": 4.283367252199552e-05}, {"token": "f10", "mean_abs_shap": 4.5351091937600476e-08}, {"token": "f9", "mean_abs_shap": 1.401894098422835e-09}], "LogicErrorNegation": [{"token": "f11", "mean_abs_shap": 0.04756816419194961}, {"token": "f4", "mean_abs_shap": 0.03909919981653507}, {"token": "f8", "mean_abs_shap": 0.020727033697822347}, {"token": "f6", "mean_abs_shap": 0.002684452262372799}, {"token": "f7", "mean_abs_shap": 0.001078960341425545}, {"token": "f5", "mean_abs_shap": 0.0008039420084639472}, {"token": "f2", "mean_abs_shap": 0.0006457608828429909}, {"token": "f3", "mean_abs_shap": 0.0005270032879284544}, {"token": "f0", "mean_abs_shap": 0.0003089447911006602}, {"token": "f1", "mean_abs_shap": 0.0001343965949240822}, {"token": "f10", "mean_abs_shap": 1.8239184173394896e-06}, {"token": "f9", "mean_abs_shap": 2.0795351236230645e-09}], "NoError": [{"token": "f4", "mean_abs_shap": 0.010580577693672311}, {"token": "f8", "mean_abs_shap": 0.005312753164070472}, {"token": "f11", "mean_abs_shap": 0.004785063789716292}, {"token": "f3", "mean_abs_shap": 0.0013527917277136926}, {"token": "f6", "mean_abs_shap": 0.0005263032747320604}, {"token": "f5", "mean_abs_shap": 0.000151632067132314}, {"token": "f7", "mean_abs_shap": 0.00013395009134794256}, {"token": "f0", "mean_abs_shap": 5.3526469359521134e-05}, {"token": "f2", "mean_abs_shap": 5.311419223307178e-05}, {"token": "f1", "mean_abs_shap": 7.049741652800475e-06}, {"token": "f10", "mean_abs_shap": 2.6637395355287187e-07}, {"token": "f9", "mean_abs_shap": 1.0504330496300877e-09}], "RecursionErrorPotential": [{"token": "f11", "mean_abs_shap": 0.0016028063502117242}, {"token": "f4", "mean_abs_shap": 0.001527893113483818}, {"token": "f8", "mean_abs_shap": 0.0001452965923876022}, {"token": "f1", "mean_abs_shap": 9.054160520195113e-05}, {"token": "f0", "mean_abs_shap": 7.885630136318083e-05}, {"token": "f6", "mean_abs_shap": 5.2506200962244684e-05}, {"token": "f7", "mean_abs_shap": 2.0396369139580854e-05}, {"token": "f5", "mean_abs_shap": 1.2067788833878558e-05}, {"token": "f3", "mean_abs_shap": 1.0490765624146846e-05}, {"token": "f2", "mean_abs_shap": 3.8075196965818306e-06}, {"token": "f10", "mean_abs_shap": 1.433947710322266e-09}, {"token": "f9", "mean_abs_shap": 5.593990284559747e-10}], "SyntaxErrorMismatchedParen": [{"token": "f4", "mean_abs_shap": 0.010024076527012737}, {"token": "f11", "mean_abs_shap": 0.005825336959209306}, {"token": "f3", "mean_abs_shap": 0.002401172705135967}, {"token": "f8", "mean_abs_shap": 0.001669125092682424}, {"token": "f6", "mean_abs_shap": 0.0005206884174343089}, {"token": "f7", "mean_abs_shap": 0.00043971704039577885}, {"token": "f0", "mean_abs_shap": 0.00036586544337584086}, {"token": "f5", "mean_abs_shap": 0.00017042875741174717}, {"token": "f2", "mean_abs_shap": 8.33270230751659e-05}, {"token": "f1", "mean_abs_shap": 3.0100998149032455e-05}, {"token": "f10", "mean_abs_shap": 1.521275073959866e-07}, {"token": "f9", "mean_abs_shap": 1.105552496251582e-09}], "SyntaxErrorMissingColon": [{"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "TypeErrorArity": [{"token": "f4", "mean_abs_shap": 0.0025750586895205672}, {"token": "f11", "mean_abs_shap": 0.0016734581732898016}, {"token": "f8", "mean_abs_shap": 0.0005267711977579946}, {"token": "f2", "mean_abs_shap": 0.00036539156925862707}, {"token": "f7", "mean_abs_shap": 0.00031846382697732766}, {"token": "f6", "mean_abs_shap": 0.00025135149407933764}, {"token": "f5", "mean_abs_shap": 0.00016821002116658733}, {"token": "f0", "mean_abs_shap": 7.225697698642088e-05}, {"token": "f3", "mean_abs_shap": 5.025544978303395e-05}, {"token": "f1", "mean_abs_shap": 1.656594368608393e-06}, {"token": "f10", "mean_abs_shap": 5.291810449278131e-10}, {"token": "f9", "mean_abs_shap": 2.7820057602679884e-10}], "TypeErrorBadAdd": [{"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "TypeErrorBadKwarg": [{"token": "f6", "mean_abs_shap": 0.023425281666968095}, {"token": "f7", "mean_abs_shap": 0.01687060223536303}, {"token": "f4", "mean_abs_shap": 0.005623136525573247}, {"token": "f5", "mean_abs_shap": 0.0009998063096064087}, {"token": "f8", "mean_abs_shap": 0.0006780312662930911}, {"token": "f11", "mean_abs_shap": 0.0004020671571965501}, {"token": "f0", "mean_abs_shap": 0.000324246031214054}, {"token": "f1", "mean_abs_shap": 7.003929999532207e-05}, {"token": "f3", "mean_abs_shap": 3.197584821139e-06}, {"token": "f2", "mean_abs_shap": 7.631253533126364e-07}, {"token": "f10", "mean_abs_shap": 8.112666117491265e-10}, {"token": "f9", "mean_abs_shap": 2.6765502201162795e-10}], "VariableError": [{"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}]}, "n_samples_used": 100}