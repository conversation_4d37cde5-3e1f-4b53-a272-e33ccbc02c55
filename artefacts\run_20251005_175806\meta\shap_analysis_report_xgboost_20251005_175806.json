{"metadata": {"model_name": "XGBoost", "run_id": "20251005_175806", "timestamp": "2025-10-05T19:38:18.101565+00:00", "n_samples": 100, "n_features": 71, "shap_values_shape": [100, 71, 12], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_word_count", "mean_abs_shap": 0.025996334231462324, "std_abs_shap": 0.061076537261821266, "max_abs_shap": 0.6794561441297641, "feature_index": 70}, {"rank": 2, "feature_name": "feat_char_count", "mean_abs_shap": 0.02485138721296917, "std_abs_shap": 0.06016353240806126, "max_abs_shap": 0.6934788885440812, "feature_index": 13}, {"rank": 3, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.021892401843207347, "std_abs_shap": 0.05918105957516841, "max_abs_shap": 0.5878345899293692, "feature_index": 49}, {"rank": 4, "feature_name": "feat_return_statements", "mean_abs_shap": 0.02120890059352294, "std_abs_shap": 0.06660712695156597, "max_abs_shap": 0.7367904708010473, "feature_index": 58}, {"rank": 5, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.017292450875376664, "std_abs_shap": 0.03762949572504661, "max_abs_shap": 0.25895903833460143, "feature_index": 44}, {"rank": 6, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.013788922401932921, "std_abs_shap": 0.028852477819439285, "max_abs_shap": 0.184457131611945, "feature_index": 55}, {"rank": 7, "feature_name": "feat_function_calls", "mean_abs_shap": 0.008558543231554777, "std_abs_shap": 0.020405113750347367, "max_abs_shap": 0.1976230970196788, "feature_index": 25}, {"rank": 8, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.008170092204409053, "std_abs_shap": 0.02267770539507806, "max_abs_shap": 0.20929791798028394, "feature_index": 3}, {"rank": 9, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.0063851451307362335, "std_abs_shap": 0.01535308577405825, "max_abs_shap": 0.13116433362135219, "feature_index": 63}, {"rank": 10, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.0062956533638729405, "std_abs_shap": 0.018351049707474996, "max_abs_shap": 0.286601520793588, "feature_index": 0}, {"rank": 11, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.006226730820552645, "std_abs_shap": 0.01692427130833816, "max_abs_shap": 0.16516360400406768, "feature_index": 64}, {"rank": 12, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.005738195667402585, "std_abs_shap": 0.015193984987222103, "max_abs_shap": 0.14687209290739608, "feature_index": 5}, {"rank": 13, "feature_name": "feat_assignments", "mean_abs_shap": 0.005521201349402949, "std_abs_shap": 0.015010504794527096, "max_abs_shap": 0.12763778233728681, "feature_index": 2}, {"rank": 14, "feature_name": "feat_if_statements", "mean_abs_shap": 0.005202899340816714, "std_abs_shap": 0.02372467654112909, "max_abs_shap": 0.33350865804155994, "feature_index": 31}, {"rank": 15, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.004890571497193865, "std_abs_shap": 0.015613338497439002, "max_abs_shap": 0.19032029505977663, "feature_index": 48}, {"rank": 16, "feature_name": "feat_avg_function_complexity", "mean_abs_shap": 0.0027511283870017343, "std_abs_shap": 0.007973444387314104, "max_abs_shap": 0.05574516643319464, "feature_index": 6}, {"rank": 17, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.002551990654135184, "std_abs_shap": 0.010824174548948639, "max_abs_shap": 0.12601423469073902, "feature_index": 12}, {"rank": 18, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.002340030456710055, "std_abs_shap": 0.010776738104583537, "max_abs_shap": 0.1458070915996357, "feature_index": 51}, {"rank": 19, "feature_name": "feat_max_nesting_depth", "mean_abs_shap": 0.0019358268068564992, "std_abs_shap": 0.007956247057190738, "max_abs_shap": 0.06879939818615079, "feature_index": 52}, {"rank": 20, "feature_name": "feat_import_statements", "mean_abs_shap": 0.0016634622915105216, "std_abs_shap": 0.008663010156233395, "max_abs_shap": 0.16325660728055333, "feature_index": 32}], "total_importance": 0.2077633386478726, "top_10_importance_ratio": 0.743344962081094}, "feature_statistics": {"mean_importance": 0.002926244206308065, "median_importance": 0.00014632405113797924, "std_importance": 0.005957781722641862, "max_importance": 0.025996334231462324, "min_importance": 0.0, "importance_concentration": 0.535423985196329}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.017292450875376664, "rank": 44}, {"name": "feat_nodes_with_children", "importance": 0.013788922401932921, "rank": 55}, {"name": "feat_total_nodes", "importance": 0.0063851451307362335, "rank": 63}, {"name": "feat_tree_depth", "importance": 0.006226730820552645, "rank": 64}, {"name": "feat_max_nesting_depth", "importance": 0.0019358268068564992, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.005202899340816714, "rank": 31}, {"name": "feat_if_else_chains", "importance": 0.0009604647831362802, "rank": 30}, {"name": "feat_for_loops", "importance": 0.0006995251872362169, "rank": 24}, {"name": "feat_nested_ifs", "importance": 0.00015928502935381604, "rank": 53}, {"name": "feat_nested_loops", "importance": 0.00014632405113797924, "rank": 54}, {"name": "feat_string_formatting", "importance": 0.00010907346853228371, "rank": 61}, {"name": "feat_keyword_for_count", "importance": 5.651583740352063e-05, "rank": 36}, {"name": "feat_keyword_if_count", "importance": 1.4825250597302744e-05, "rank": 37}, {"name": "feat_while_loops", "importance": 7.611015043093008e-06, "rank": 68}, {"name": "feat_try_statements", "importance": 7.469883047552778e-06, "rank": 67}, {"name": "feat_try_except_blocks", "importance": 5.282070945946266e-06, "rank": 65}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}], "complexity_features": [{"name": "feat_avg_branching_factor", "importance": 0.005738195667402585, "rank": 5}, {"name": "feat_max_branching_factor", "importance": 0.004890571497193865, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.0027511283870017343, "rank": 6}, {"name": "feat_cyclomatic_complexity", "importance": 0.0015040772877257067, "rank": 19}, {"name": "feat_max_function_complexity", "importance": 0.0003871771240325484, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.021892401843207347, "rank": 49}, {"name": "feat_function_calls", "importance": 0.008558543231554777, "rank": 25}, {"name": "feat_attribute_access", "importance": 0.008170092204409053, "rank": 3}, {"name": "feat_assignments", "importance": 0.005521201349402949, "rank": 2}, {"name": "feat_builtin_calls", "importance": 0.002551990654135184, "rank": 12}, {"name": "feat_aug_assignments", "importance": 9.276874706160544e-05, "rank": 4}], "other_features": [{"name": "feat_word_count", "importance": 0.025996334231462324, "rank": 70}, {"name": "feat_char_count", "importance": 0.02485138721296917, "rank": 13}, {"name": "feat_return_statements", "importance": 0.02120890059352294, "rank": 58}, {"name": "feat_arithmetic_ops", "importance": 0.0062956533638729405, "rank": 0}, {"name": "feat_max_function_params", "importance": 0.002340030456710055, "rank": 51}, {"name": "feat_import_statements", "importance": 0.0016634622915105216, "rank": 32}, {"name": "feat_keyword_def_count", "importance": 0.0016124373162280808, "rank": 34}, {"name": "feat_subscript_access", "importance": 0.0015603175501658869, "rank": 62}, {"name": "feat_comparison_ops", "importance": 0.0015599730618710353, "rank": 16}, {"name": "feat_equals_count", "importance": 0.001468232829587054, "rank": 23}, {"name": "feat_line_count", "importance": 0.001404549073503641, "rank": 45}, {"name": "feat_bracket_count", "importance": 0.0007320030322264345, "rank": 10}, {"name": "feat_colon_count", "importance": 0.0006681042760155242, "rank": 15}, {"name": "feat_list_comprehensions", "importance": 0.0003849001548330229, "rank": 46}, {"name": "feat_paren_count", "importance": 0.0003410213540819433, "rank": 56}, {"name": "feat_keyword_import_count", "importance": 0.00023175795327995505, "rank": 38}, {"name": "feat_keyword_return_count", "importance": 0.00010937987519983578, "rank": 39}, {"name": "feat_dict_comprehensions", "importance": 8.469679138670042e-05, "rank": 21}, {"name": "feat_boolean_ops", "importance": 6.487411478767739e-05, "rank": 8}, {"name": "feat_list_comps", "importance": 2.4088989005470565e-05, "rank": 47}, {"name": "feat_function_defs", "importance": 2.3610919093729125e-05, "rank": 26}, {"name": "feat_has_syntax_error", "importance": 2.0490057095345144e-05, "rank": 29}, {"name": "feat_dict_comps", "importance": 1.9608748594888527e-05, "rank": 22}, {"name": "feat_keyword_class_count", "importance": 1.794285264299406e-05, "rank": 33}, {"name": "feat_generator_expressions", "importance": 7.240946532783927e-06, "rank": 27}, {"name": "feat_lambda_functions", "importance": 6.31147688038095e-06, "rank": 42}, {"name": "feat_lambda_usage", "importance": 2.815209400171906e-06, "rank": 43}, {"name": "feat_decorator_usage", "importance": 2.630850260688668e-06, "rank": 20}, {"name": "feat_class_defs", "importance": 2.199025936713989e-06, "rank": 14}, {"name": "feat_generator_exps", "importance": 1.8840933816743595e-06, "rank": 28}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_brace_count", "importance": 0.0, "rank": 9}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.04562907603545496, "control_flow_features": 0.007369275917250705, "complexity_features": 0.01527114996335644, "syntactic_features": 0.04678699802977092, "other_features": 0.09270683870203958}}}, "visualisations_generated": ["shap_summary_xgboost_20251005_175806.png", "shap_bar_xgboost_20251005_175806.png", "shap_waterfall_xgboost_sample1_20251005_175806.png", "shap_waterfall_xgboost_sample2_20251005_175806.png", "shap_waterfall_xgboost_sample3_20251005_175806.png", "shap_dependence_xgboost_feat1_20251005_175806.png", "shap_dependence_xgboost_feat2_20251005_175806.png"]}