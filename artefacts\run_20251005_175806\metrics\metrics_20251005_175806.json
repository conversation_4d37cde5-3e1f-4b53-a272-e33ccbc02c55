{"run_id": "20251005_175806", "dataset_hash": "1e391aa3e9116d35e6d2acf9d063015252027ae62d95420a17e7b99b5ec86d8a", "experiment": {"grouping": true, "include_no_error": true, "label_space": "grouped", "balance_mode": "errors+noerror"}, "selection": {"metric": "cv", "scores": {"Logistic Regression": 0.28500000000000003, "Random Forest": 0.3325, "SVM (RBF)": 0.32625, "SVM (Linear)": 0.39999999999999997, "Naive Bayes": 0.17500000000000002, "Neural Network": 0.34875, "Gradient Boosting": 0.43, "K-Nearest Neighbors": 0.22, "Decision Tree": 0.2575, "XGBoost": 0.42250000000000004, "HistGradient Boosting": 0.42874999999999996}, "cv_means": {"Logistic Regression": 0.28500000000000003, "Random Forest": 0.3325, "SVM (RBF)": 0.32625, "SVM (Linear)": 0.39999999999999997, "Naive Bayes": 0.17500000000000002, "Neural Network": 0.34875, "Gradient Boosting": 0.43, "K-Nearest Neighbors": 0.22, "Decision Tree": 0.2575, "XGBoost": 0.42250000000000004, "HistGradient Boosting": 0.42874999999999996}, "cv_stds": {"Logistic Regression": 0.021602468994692862, "Random Forest": 0.018484227510682356, "SVM (RBF)": 0.04441752657078808, "SVM (Linear)": 0.007071067811865455, "Naive Bayes": 0.03188521078284832, "Neural Network": 0.015478479684172243, "Gradient Boosting": 0.04143267631552019, "K-Nearest Neighbors": 0.043779751788545665, "Decision Tree": 0.025331140255951116, "XGBoost": 0.032015621187164243, "HistGradient Boosting": 0.0400780488547035}, "times_sec": {"Logistic Regression": 0.10606790008023381, "Random Forest": 0.1767802000977099, "SVM (RBF)": 0.2702297999057919, "SVM (Linear)": 0.2126407001633197, "Naive Bayes": 0.008964800043031573, "Neural Network": 0.7145243000704795, "Gradient Boosting": 6.67736169998534, "K-Nearest Neighbors": 0.0065213998313993216, "Decision Tree": 0.01482609985396266, "XGBoost": 0.5332702999003232, "HistGradient Boosting": 0.7087743999436498}, "best_model_name": "Grad<PERSON>"}, "cv_details": {"Logistic Regression": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.28500000000000003, "cv_std_accuracy": 0.021602468994692862, "cv_fold_scores": [0.275, 0.315, 0.265, 0.285], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.10606790008023381, "cv_time_sec": 2.3866396001540124, "classifier_params": {"C": 0.1, "class_weight": "balanced", "dual": false, "fit_intercept": true, "intercept_scaling": 1, "l1_ratio": null, "max_iter": 2000, "multi_class": "auto", "n_jobs": null, "penalty": "l2", "random_state": 25, "solver": "liblinear", "tol": 0.0001, "verbose": 0, "warm_start": false}}, "Random Forest": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.3325, "cv_std_accuracy": 0.018484227510682356, "cv_fold_scores": [0.355, 0.31, 0.335, 0.33], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.1767802000977099, "cv_time_sec": 1.4030716000124812, "classifier_params": {"bootstrap": true, "ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 15, "max_features": "sqrt", "max_leaf_nodes": null, "max_samples": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 2, "min_samples_split": 5, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "n_estimators": 200, "n_jobs": 21, "oob_score": false, "random_state": 25, "verbose": 0, "warm_start": false}}, "SVM (RBF)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.32625, "cv_std_accuracy": 0.04441752657078808, "cv_fold_scores": [0.345, 0.26, 0.345, 0.355], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.2702297999057919, "cv_time_sec": 3.024088399950415, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "rbf", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "SVM (Linear)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.39999999999999997, "cv_std_accuracy": 0.007071067811865455, "cv_fold_scores": [0.395, 0.4, 0.395, 0.41], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.2126407001633197, "cv_time_sec": 3.125307200010866, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "linear", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "Naive Bayes": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.17500000000000002, "cv_std_accuracy": 0.03188521078284832, "cv_fold_scores": [0.17, 0.165, 0.22, 0.145], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.008964800043031573, "cv_time_sec": 1.672273200005293, "classifier_params": {"priors": null, "var_smoothing": 1e-09}}, "Neural Network": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.34875, "cv_std_accuracy": 0.015478479684172243, "cv_fold_scores": [0.35, 0.335, 0.34, 0.37], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.7145243000704795, "cv_time_sec": 3.1288697000127286, "classifier_params": {"activation": "relu", "alpha": 0.01, "batch_size": "auto", "beta_1": 0.9, "beta_2": 0.999, "early_stopping": true, "epsilon": 1e-08, "hidden_layer_sizes": [100, 50], "learning_rate": "constant", "learning_rate_init": 0.001, "max_fun": 15000, "max_iter": 300, "momentum": 0.9, "n_iter_no_change": 10, "nesterovs_momentum": true, "power_t": 0.5, "random_state": 25, "shuffle": true, "solver": "adam", "tol": 0.0001, "validation_fraction": 0.2, "verbose": false, "warm_start": false}}, "Gradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.43, "cv_std_accuracy": 0.04143267631552019, "cv_fold_scores": [0.37, 0.445, 0.44, 0.465], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 6.67736169998534, "cv_time_sec": 6.511882500024512, "best_params": {"classifier__subsample": 1.0, "classifier__n_estimators": 200, "classifier__max_depth": 4, "classifier__learning_rate": 0.1}, "best_cv": 0.43, "classifier_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 200, "n_iter_no_change": null, "random_state": 25, "subsample": 1.0, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}, "K-Nearest Neighbors": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.22, "cv_std_accuracy": 0.043779751788545665, "cv_fold_scores": [0.26, 0.19, 0.175, 0.255], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.0065213998313993216, "cv_time_sec": 3.285156999947503, "classifier_params": {"base_estimator": "KNeighborsClassifier(metric='euclidean', weights='distance')"}}, "Decision Tree": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.2575, "cv_std_accuracy": 0.025331140255951116, "cv_fold_scores": [0.225, 0.25, 0.275, 0.28], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.01482609985396266, "cv_time_sec": 1.1687570000067353, "classifier_params": {"ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 10, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 5, "min_samples_split": 10, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "random_state": 25, "splitter": "best"}}, "XGBoost": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.42250000000000004, "cv_std_accuracy": 0.032015621187164243, "cv_fold_scores": [0.375, 0.445, 0.435, 0.435], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.5332702999003232, "cv_time_sec": 3.7625551000237465, "classifier_params": {"base_estimator": "XGBClassifier(base_score=None, booster=None, callbacks=None,\n              colsample_bylevel=None, colsample_bynode=None,\n              colsample_bytree=0.8, device=None, early_stopping_rounds=None,\n              enable_categorical=False, eval_metric='mlogloss',\n              feature_types=None, feature_weights=None, gamma=None,\n              grow_policy=None, importance_type=None,\n              interaction_constraints=None, learning_rate=0.2, max_bin=None,\n              max_cat_threshold=None, max_cat_to_onehot=None,\n              max_delta_step=None, max_depth=6, max_leaves=None,\n              min_child_weight=None, missing=nan, monotone_constraints=None,\n              multi_strategy=None, n_estimators=150, n_jobs=21,\n              num_parallel_tree=None, ...)"}}, "HistGradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.42874999999999996, "cv_std_accuracy": 0.0400780488547035, "cv_fold_scores": [0.37, 0.445, 0.44, 0.46], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.7087743999436498, "cv_time_sec": 2.1116185998544097, "classifier_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 0.0, "learning_rate": 0.2, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 31, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}}, "efficiency": {"inference_time_sec": 0.0044686999171972275, "per_sample_latency_sec": 4.468699917197228e-05, "training_time_sec_by_model": {"Logistic Regression": 0.10606790008023381, "Random Forest": 0.1767802000977099, "SVM (RBF)": 0.2702297999057919, "SVM (Linear)": 0.2126407001633197, "Naive Bayes": 0.008964800043031573, "Neural Network": 0.7145243000704795, "Gradient Boosting": 6.67736169998534, "K-Nearest Neighbors": 0.0065213998313993216, "Decision Tree": 0.01482609985396266, "XGBoost": 0.5332702999003232, "HistGradient Boosting": 0.7087743999436498}}, "model_info": {"n_classes": null, "feature_dimension": null, "has_predict_proba": true}, "data_info": {"n_train": 400, "n_test": 100}, "calibration": {"bins": 10, "pre": {"calibration_frac_pos": [0.5, 0.4166666666666667, 0.5, 0.4166666666666667, 0.5, 0.6304347826086957], "calibration_mean_pred": [0.48966550779548984, 0.5536599143987962, 0.6336150226111271, 0.7535168765976024, 0.8594936090966718, 0.9818278559604537], "brier_score": 0.33276106438661157, "ece": 0.2897958880661371, "mce": 0.3594936090966716, "brier_reliability": 0.09513496227529698, "brier_resolution": 0.007892753623188406, "brier_uncertainty": 0.2484, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 0, 0, 4, 12, 10, 12, 16, 46], "bin_acc": [null, null, null, null, 0.5, 0.4166666666666667, 0.5, 0.4166666666666667, 0.5, 0.6304347826086957], "bin_conf": [null, null, null, null, 0.48966550779548984, 0.5536599143987962, 0.6336150226111271, 0.7535168765976022, 0.8594936090966716, 0.9818278559604537], "plot_frac_pos": [0.5, 0.4166666666666667, 0.5, 0.4166666666666667, 0.5, 0.6304347826086957], "plot_mean_pred": [0.48966550779548984, 0.5536599143987962, 0.6336150226111271, 0.7535168765976022, 0.8594936090966716, 0.9818278559604537]}, "post": null, "plots": {"pre": "artefacts\\run_20251005_175806\\figures\\calibration_reliability_curve_-_gradient_boosting.png", "post": null, "comparison": null}}, "bootstrap_ci": {"accuracy_ci95": [0.42975, 0.6302500000000001], "macro_f1_ci95": [0.2383268155272719, 0.45987312359859744], "balanced_accuracy_ci95": [0.256629179986876, 0.47270552097387614]}, "artefact": {"path": "artefacts\\run_20251005_175806\\models\\gradient_boosting_20251005_175806_2025-10-05T17-59-32.525621+00-00.joblib", "size_bytes": 6385581, "size_bytes_by_model": {"Logistic Regression": 32562, "Random Forest": 9444811, "SVM (RBF)": 1031338, "SVM (Linear)": 614794, "Naive Bayes": 41370, "Neural Network": 342775, "Gradient Boosting": 6385581, "K-Nearest Neighbors": 1408825, "Decision Tree": 61811, "XGBoost": 2165837, "HistGradient Boosting": 1013800}}, "best_model_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 200, "n_iter_no_change": null, "random_state": 25, "subsample": 1.0, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}, "pr_curves_plot": "artefacts\\run_20251005_175806\\figures\\pr_curves_pr_curves_-_gradient_boosting.png", "error_analysis": {"per_class_csv": "artefacts\\run_20251005_175806\\tables\\per_class_metrics_20251005_175806.csv", "confusions_csv": "artefacts\\run_20251005_175806\\tables\\top_confusions_20251005_175806.csv", "misclassifications_csv": "artefacts\\run_20251005_175806\\tables\\misclassifications_20251005_175806.csv", "misclassifications_shap_csv": null, "slices_csv": "artefacts\\run_20251005_175806\\tables\\slices_20251005_175806.csv"}, "slices": {"metrics_csv": null, "settings": {"rare_threshold": 5, "length_short_max": 5, "length_long_min": 21}}, "do_final_test": true, "provenance": {"timestamp_utc": "2025-10-05T17:59:32.809583+00:00", "python_version": "3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]", "platform": "Windows-11-10.0.26100-SP0", "os_version": "10.0.26100", "hostname": "DESKTOP-KNG670J", "cpu_count": 32, "cpu_arch": "AMD64", "gpu": ["NVIDIA GeForce RTX 4070 Ti SUPER"], "libraries": {"numpy": "1.26.4", "scikit-learn": "1.4.2", "pandas": "2.2.3", "joblib": "1.4.2", "shap": "0.48.0", "xgboost": "3.0.5", "imbalanced-learn": "0.12.3", "matplotlib": "3.8.4", "seaborn": "0.13.2"}}}